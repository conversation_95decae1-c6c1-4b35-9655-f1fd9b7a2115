<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.hanyi</groupId>
    <artifactId>parent-worker</artifactId>
    <version>${revision}.${sha1}-${changelist}</version>
    <packaging>pom</packaging>

    <name>parent</name>
    <url>http://maven.apache.org</url>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.5</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>14</java.version>
        <maven.compiler.plugin.version>3.5.1</maven.compiler.plugin.version>
        <maven.compiler.source>14</maven.compiler.source>
        <maven.compiler.target>14</maven.compiler.target>
        <spring.version>2.4.5</spring.version>
        <lombok.version>1.18.30</lombok.version>
        <hanyi.expression.version>0.4.10-SNAPSHOT</hanyi.expression.version>
        <befun.nlp.core.version>0.2.4-SNAPSHOT</befun.nlp.core.version>
        <befun.nlp.model.version>0.1.2-SNAPSHOT</befun.nlp.model.version>
        <mynlp.version>4.0.0</mynlp.version>
        <jooq.version>3.16.0</jooq.version>
        <mynlp.resource.version>1.0.0</mynlp.resource.version>
        <hanyi.common.ip-resolver.version>0.1.7-SNAPSHOT</hanyi.common.ip-resolver.version>
        <!--   worker 的版本号  https://maven.apache.org/maven-ci-friendly.html  -->
        <revision>1.11.9</revision>
        <sha1>101</sha1>
        <changelist>BSH</changelist>
        <core.version>**********-${changelist}</core.version>
        <auth.version>**********-${changelist}</auth.version>
        <ctm.version>**********-${changelist}</ctm.version>
        <survey.version>**********-${changelist}</survey.version>
        <bi.version>**********-${changelist}</bi.version>
        <current.version>${revision}.${sha1}-${changelist}</current.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.200</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.befun.task</groupId>
                <artifactId>befun-task</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun</groupId>
                <artifactId>befun-core</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.extension</groupId>
                <artifactId>befun-x-pack</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-core</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-pay</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-trigger</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.bi</groupId>
                <artifactId>bi-template</artifactId>
                <version>${bi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.bi</groupId>
                <artifactId>bi-dashboard</artifactId>
                <version>${bi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.bi</groupId>
                <artifactId>bi-trigger</artifactId>
                <version>${bi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-customer-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-common-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-data-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-event-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-journey-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-trigger</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-base</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <version>${survey.version}</version>
                <artifactId>survey-core</artifactId>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-client</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-trigger</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-core</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-compat</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-task</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-runner</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-event</artifactId>
                <version>${current.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>cem-compat</module>
        <module>cem-task</module>
        <module>cem-event</module>
        <module>cem-runner</module>
        <module>cem-core</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <encoding>UTF-8</encoding>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources</generatedSourcesDirectory>
                    <annotationProcessorPaths>
                        <annotationProcessorPath>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>org.befun</groupId>
                            <artifactId>befun-core</artifactId>
                            <version>${core.version}</version>
                        </annotationProcessorPath>
                    </annotationProcessorPaths>
                    <annotationProcessors>
                        <annotationProcessor>lombok.launch.AnnotationProcessorHider$AnnotationProcessor
                        </annotationProcessor>
                        <annotationProcessor>org.befun.core.annotation.AnnotationProcessor
                        </annotationProcessor>
                    </annotationProcessors>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
