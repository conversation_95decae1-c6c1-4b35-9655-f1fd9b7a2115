package cn.hanyi.cem.worker.controller;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskNotifyWarningDto;
import cn.hanyi.cem.core.dto.task.TaskProcessWarningDto;
import cn.hanyi.cem.core.dto.task.TaskResponseDownloadDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.task.consumer.response.ResponseDownloadConsumer;
import cn.hanyi.cem.task.consumer.warning.RuleRerunTaskConsumer;
import cn.hanyi.cem.worker.dto.AddTaskChangeOrgVersionDto;
import cn.hanyi.cem.worker.runners.JourneyWarningScheduling;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Tag(name = "手动")
@Slf4j
@Validated
@RestController
@RequestMapping("task")
public class WorkerTaskController {

    @Autowired(required = false)
    private JourneyWarningScheduling journeyWarningScheduling;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private ResponseDownloadConsumer responseDownloadConsumer;

    @Autowired
    private RuleRerunTaskConsumer ruleRerunTaskConsumer;

    @Autowired
    private UserTaskService taskProgressService;

    @Operation(summary = "添加任务-企业版本变化")
    @PostMapping("add/changeOrgVersion")
    public void addTask(@Valid @NotNull @RequestBody AddTaskChangeOrgVersionDto dto) {
        taskProducerHelper.addTask(TaskType.ORG_CHANGE_NOTIFY, "apiAdd", 0L, dto.mapToParam());
    }

    @Operation(summary = "预警通知")
    @PostMapping("warning/trigger")
    public void triggerWarning(@RequestBody TaskNotifyWarningDto dto) {

        taskProducerHelper.warningNotify(dto);
    }

    @Operation(summary = "手动下载答卷")
    @PostMapping("response/download/{taskId}")
    public void responseDownload(@PathVariable Long taskId) {

        var taskProgress = taskProgressService.require(taskId);
        var param = JsonHelper.toList(taskProgress.getParams(), Map.class).get(0);
        var dto = new TaskResponseDownloadDto();
        dto.setTaskProgressId(taskId);
        dto.setSurveyId((Long) param.get("surveyId"));
        dto.setData(JsonHelper.toJson(param.get("downloadDto")));
        responseDownloadConsumer.consumer(null, dto);
    }


    @Operation(summary = "手动重跑事件")
    @PostMapping("events/rerun")
    public void rerunEvent(@RequestBody TaskProcessWarningDto dto) {
        ruleRerunTaskConsumer.unlock(dto.getRuleId());
        ruleRerunTaskConsumer.consumer(null, dto);
    }

}
