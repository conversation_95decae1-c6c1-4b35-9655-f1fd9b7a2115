package cn.hanyi.cem.worker.dataaccess;

import cn.hanyi.cem.worker.property.DataAccessBackupProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.DateHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
public class DataAccessBackupHelper {

    @Autowired
    private DataAccessBackupProperties properties;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private DataAccessBackupHelper self;

    public void backup() {
        try {
            long maxId = maxId();
            if (maxId <= 0) {
                return;
            }
            long nextId = nextId();
            if (nextId >= maxId) {
                return;
            }
            AtomicLong count = new AtomicLong(0);
            do {
                nextId = self.backupBatch(nextId, maxId, properties.getBatchSize(), count);
            } while (nextId > 0);
            log.info("已完成备份(data_access)，共{}条", count.get());
        } catch (Throwable e) {
            log.error("备份(data_access)失败", e);
        }
    }

    private long maxId() {
        LocalDate maxDate = LocalDate.now().minusDays(properties.getRetainDays());
        log.info("开始备份(data_access){}之前的数据", maxDate);
        long ms = DateHelper.toDate(maxDate).getTime();
        return (ms - 1619827200000L) << 16;
    }

    private long nextId() {
        // select id, create_time createTime from %s order by id desc limit 1
        String sql = String.format(properties.getSqlFormatNextId(), properties.getBackupTable());
        Value value = nativeSqlHelper.queryObject(sql, Value.class);
        if (value == null) {
            log.info("没有备份(data_access)记录,开始从0开始备份");
            return 0L;
        } else {
            log.info("已存在备份(data_access)记录,开始从{}({})开始备份", value.getId(), value.getCreateTime());
            return value.getId();
        }
    }

    @Transactional
    public long backupBatch(long nextId, long maxId, int batchSize, AtomicLong count) {
        // select id, create_time createTime from data_access_cell where id > %d and id < %d order by id asc limit %d
        String sql = String.format(properties.getSqlFormatBatch(), nextId, maxId, batchSize);
        List<Value> values = nativeSqlHelper.queryListObject(sql, Value.class);
        if (CollectionUtils.isNotEmpty(values)) {
            long start = values.get(0).getId();
            long end = values.get(values.size() - 1).getId();
            // insert into %s (`id`, `access_id`, `status`, `message_id`, `message_data`, `extra_data`, `parsed_params`, `create_time`, `modify_time`) select * from data_access_cell where id >= %d and id <= %d
            String backupSql = String.format(properties.getSqlFormatInsert(), properties.getBackupTable(), start, end);
            nativeSqlHelper.update(backupSql);
            if (properties.isTrim()) {
                // delete from data_access_cell where id >= %d and id <= %d
                String trimSql = String.format(properties.getSqlFormatDelete(), start, end);
                nativeSqlHelper.update(trimSql);
            }
            count.addAndGet(values.size());
            log.info("已备份(data_access)记录：id ({}-{})，共{}条", start, end, values.size());
            return end;
        }
        return -1;
    }

    @Getter
    @Setter
    public static class Value {
        private Long id;
        private Date createTime;
    }
}
