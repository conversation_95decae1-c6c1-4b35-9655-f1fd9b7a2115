package cn.hanyi.cem.worker.runners;

import static cn.hanyi.cem.worker.property.SendManageRemindProperties.CRON;
import static cn.hanyi.cem.worker.property.SendManageRemindProperties.PREFIX;

import cn.hanyi.ctm.dto.SurveyLinkDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.DataAccessCell;
import cn.hanyi.ctm.sendmanage.SendMangeDataAccessHelper;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.ctm.service.DataAccessCellService;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendCompositedDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.entity.Link;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(prefix = PREFIX, value = "enabled", havingValue = "true")
public class SendManageRemindScheduling extends BaseRunner {
    @Autowired
    private SendManageRepository sendManageRepository;
    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;
    @Autowired
    private TaskProducerHelper taskProducerHelper;
    @Autowired
    private SendMangeDataAccessHelper sendMangeDataAccessHelper;
    @Autowired
    private LinkService linkService;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private DataAccessCellService dataAccessCellService;

    private final static String logPrefix = "定时任务-发送管理催答任务:";
    private ExecutorService executorService = new ThreadPoolExecutor(0, 10, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(10), Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.DiscardPolicy());



    @PostConstruct
    public void init() {
        log.info("定时任务启动：{} ",
                logPrefix
        );
    }

    @Scheduled(cron = CRON)
    public void scheduling() {
        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        int page = 0;
        int size = 10;
        boolean hasNext = true;

        try {
            log.info("{} 获取系统中需要催答的发送管理", logPrefix);
            Semaphore semaphore = new Semaphore(size);
            while (hasNext) {
                Page<SendManage> sendManages = sendManageRepository.findByEnableAndEnableRemind(true, true, PageRequest.of(page, size));
                if (!sendManages.hasContent()) {
                    hasNext = false;
                } else {
                    for (SendManage sendManage : sendManages.getContent()) {
                        if (semaphore.tryAcquire(1, 30, TimeUnit.MINUTES)) {
                            executorService.execute(() -> {
                                try {
                                    remindSendManageRecord(sendManage);
                                } catch (Exception ex) {
                                    log.error("催答失败，sendManageId:{}, caused by:{}", sendManage.getId(),
                                            ex.getStackTrace());
                                } finally {
                                    semaphore.release();
                                }
                            });
                        }
                    }
                }
                page++;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} 定时催答任务异常", logPrefix);
        } finally {
            log.info("{} 定时催答任务结束", logPrefix);
            unlock();
        }
    }

    private void remindSendManageRecord(SendManage sendManage) {
        int page = 0;
        int size = 100;
        boolean hasNext = true;

        while (hasNext) {
            Page<SendManageRecord> sendManageRecords = sendManageRecordRepository.findAllBySendManageIdAndSendStatusAndReplyStatusNot(sendManage.getId(),
                    SendStatus.SEND_SUCCESS, ReplyStatus.SUBMIT, PageRequest.of(page, size));
            if (!sendManageRecords.hasContent()) {
                hasNext = false;
            } else {
                for (SendManageRecord r : sendManageRecords.getContent()) {
                    try {
                        if (checkRemind(sendManage, r)) {
                            SimpleSurvey survey = surveyRepository.findSimpleById(r.getSurveyId());
                            if (survey == null) {
                                log.info("催答问卷不存在，跳过, recordId:{}", r.getId());
                                continue;
                            }
                            Customer customer = customerService.get(r.getCustomerId());
                            if (customer == null) {
                                log.info("催答客户不存在，跳过, recordId:{}", r.getId());
                                continue;
                            }
                            DataAccessCell cell = dataAccessCellService.get(r.getSourceId());
                            if (cell == null) {
                                log.info("催答数据不存在，跳过, recordId:{}", r.getId());
                                continue;
                            }

                            TaskCustomerSendCompositedDto send = new TaskCustomerSendCompositedDto();
                            LocalDateTime remindTime = buildRemindTime(r.getSendTime());
                            SurveyLinkDto link = buildSurveyLinkDto(r.getLinkId());

                            Map<String, Object> params = JsonHelper.toMap(cell.getParsedParams());

                            if (!sendMangeDataAccessHelper.buildSendChannel(send, null, remindTime, null, sendManage, r, link, survey.getTitle(), customer, params)) {
                                log.info("催答渠道不存在，跳过, recordId:{}", r.getId());
                                continue;
                            }

                            taskProducerHelper.addTask(false, true, false, sendManage.getOrgId(), null, TaskType.CUSTOMER_SEND_COMPOSITED, sendManage.getTriggerType().name(), r.getSourceId(),
                                    JsonHelper.toJson(sendManage), buildDuration(remindTime), null);
                            r.setRemindCount((r.getRemindCount() == null ? 0 : r.getRemindCount()) + 1);
                            sendManageRecordRepository.save(r);
                            log.info("发送管理记录：{}, 催答任务发送成功", r.getId());
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        log.warn("发送管理记录：{}, 催答任务发送失败,caused by : {}",r.getId(), ex.getMessage());
                    }
                }
            }
            page++;
        }
    }

    private Boolean checkRemind(SendManage sendManage, SendManageRecord record) {
        boolean gapDayCheck;
        boolean countCheck;
        // 最后一次催答时间距今超过remindGap;
        if (record.getLatestRemindTime() != null) {
            LocalDate latestRemindDate = record.getLatestRemindTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            long gapDays = ChronoUnit.DAYS.between(latestRemindDate, LocalDate.now());
            gapDayCheck = gapDays >= sendManage.getRemindGap();
        } else {
            gapDayCheck = true;
        }
        // 催答次数小于remindStopNumber
        if (sendManage.getRemindStopNumber() != null) {
            countCheck = (record.getRemindCount() == null ? 0 : record.getRemindCount()) < sendManage.getRemindStopNumber();
        } else {
            countCheck = true;
        }

        return gapDayCheck && countCheck;
    }

//    private Boolean checkDataAccessCell(DataAccessCell cell) {
//        if (cell == null || cell.getStatus() == null || DataAccessCellStatus.cell.getStatus()) {
//            return false;
//        }
//        DataAccess dataAccess = cell.getDataAccess();
//        if (dataAccess == null || dataAccess.getEnable() == null || !dataAccess.getEnable()) {
//            return false;
//        }
//        return true;
//    }

    private Duration buildDuration(LocalDateTime remindTime) {
        if (remindTime.isBefore(LocalDateTime.now())) {
            log.debug("当前时间超过催答时间，立即发送");
            return null;
        }
        return Duration.between(LocalDateTime.now(), remindTime);
    }

    private LocalDateTime buildRemindTime(Date sendTime) {
        if (sendTime == null) {
            return null;
        }

        LocalTime sourceTime = sendTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalTime();

        // 获取今天的日期
        LocalDate today = LocalDate.now();
        return LocalDateTime.of(today, sourceTime);
    }

    private SurveyLinkDto buildSurveyLinkDto(Long linkId) {
        Link link = linkService.getLink(linkId);
        SurveyLinkDto dto = new SurveyLinkDto();
        dto.setOriginUrl(linkService.toOriginUrl(link));
        dto.setShortUrl(linkService.toShortUrl(link));
        dto.setShortCode(linkService.toShortCode(link));
        return dto;
    }

    public void triggerSendManageRemind(Long id) {
        Optional<SendManage> sendManage = sendManageRepository.findById(id);
        sendManage.ifPresent(this::remindSendManageRecord);
    }
}

