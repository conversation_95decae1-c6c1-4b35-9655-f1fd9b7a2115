xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${XMPLUS_SHORT:https://dev-t.xmplus.cn}
surveyplus:
  domain: ${SURVEYPLUS_DOMAIN:https://dev.surveyplus.cn}
system-notify.url:
  adminx: ${SYSTEMNOTIFY_URL_ADMINX:https://dev.xmplus.cn/adminx}
  wework:
    buy-version: ${SYSTEMNOTIFY_URL_WEWORK_BUYVERSION:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4443c796-a4fd-46d3-aeaf-629a9d050830}
    survey-audit: ${SYSTEMNOTIFY_URL_WEWORK_SURVEYAUDIT:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=08d90082-d7cb-46d9-b65f-7c1f44de5656}
    adminx-channel: ${SYSTEMNOTIFY_URL_WEWORK_ADMINXCHANNEL:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8ec113ed-e8b4-4870-a5d7-d92d33990ebd}
server:
  shutdown: graceful
  error:
    whitelabel:
      enabled: false
  port: ${PORT:8089}
  servlet:
    context-path: /api/worker

spring:
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        server-addr: ${NACOS_SERVER_ADDR:mse-8ba4aca6-p.nacos-ans.mse.aliyuncs.com:8848}
        group: ${NACOS_GROUP:cem}
        file-extension: ${NACOS_FILE_EXTENSION:properties}
        access-key: ${NACOS_ACCESS_KEY:}
        secret-key: ${NACOS_SECRET_KEY:}
  lifecycle:
    timeout-per-shutdown-phase: 60s
  config:
    import:
      - nacos:${NACOS_DATA_ID:cem-common}
      - nacos:${NACOS_DATA_ID:cem-worker}
      - classpath:adminx.yml
      - classpath:auth.yml
      - classpath:befun-auth-pay.yml
      - classpath:befun-metrics.yml
      - classpath:befun-sms-chuanglan.yml
      - classpath:befun-xpack-alipay.yml
      - classpath:befun-xpack-wechatopen.yml
      - classpath:befun-xpack-wechatpay.yml
      - classpath:befun-xpack-inboxmessage.yml
      - classpath:bi-worker.yml
      - classpath:bot.yml
      - classpath:extension.yml
      - classpath:file.yml
      - classpath:mail.yml
      - classpath:nlp.yml
      - classpath:survey-audit.yml
      - classpath:task.yml
      - classpath:webservice.yml
      - classpath:worker.yml
      - classpath:${DYNAMIC_DATABASE:bi-dynamic-database.yml}
      - classpath:event-result-group-config.yml
      - classpath:gmp.yml
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbc-url: ${MYSQL_URL:*********************************************************************************************************}
    username: ${MYSQL_USER:surveyuser}
    password: ${MYSQL_PASSWORD:C1E9M-P2l0t}
    connection-init-sql: SET NAMES utf8mb4
#    hikari:
#      connection-init-sql: SET NAMES utf8mb4
  jpa:
    show-sql: ${SHOW_SQL:true}
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
    rest:
      default-media-type: application/json
  redis:
    host: ${REDIS_HOST:*********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:bsxrzQ2l2zm}
    database: ${REDIS_DB:0}
    username: ${REDIS_USERNAME:}
  task:
    scheduling:
      pool:
        size: ${SPRING_TASK_SCHEDULING_POOL_SIZE:8}

hanyi:
  shorturl: ${SHORTURL:https://dev-t.xmplus.cn}

wechat:
  open:
    app_id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
    app_secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
    token: ${WECHAT_OPEN_TOKEN:surveyplus}
    aes_key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    callback: ${WECHAT_OPEN_CALLBACK:https://dev.xmplus.cn/cem/setting/account?from=wechat_open}

logging:
  level:
    root: ${LOG_LEVEL:info}
    org.befun: ${LOG_LEVEL_BEFUN:info}
    org.befun.task.lock: error
    org.befun.task.service: ${LOG_LEVEL_TASK:error}
    org.hibernate:
      SQL: ${LOG_LEVEL_SQL:error}
      type.descriptor.sql: ${LOG_LEVEL_SQL:error}
    me.chanjar.weixin.mp.api.impl.BaseWxMpServiceImpl: ${LOG_LEVEL_WECHAT:error}


feige:
  template:
    customer: default_sms_customer
ctm:
  info:
    domain: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}
  send-manage-timer:
    scheduling:
      enabled: ${CTM_SENDMANAGETIMER_SCHEDULING_ENABLE:true}
      cron: ${CTM_SENDMANAGETIMER_SCHEDULING_CRON:0 0/15 * * * *}
      interval-minutes: ${CTM_SENDMANAGETIMER_SCHEDULING_INTERVAL:15}
  send-manage-remind-timer:
    scheduling:
      enabled: ${CTM_SENDMANAGEREMINDTIMER_SCHEDULING_ENABLE:true}
      cron: ${CTM_SENDMANAGEREMINDTIMER_SCHEDULING_CRON:0 0 2 * * *}
  event-group-timer:
    scheduling:
      enabled: ${CTM_EVENTGROUPTIMER_SCHEDULING_ENABLE:true}
      cron: ${CTM_EVENTGROUPTIMER_SCHEDULING_CRON:0 4/15 * * * *}
      interval-minutes: ${CTM_SENDMANAGETIMER_SCHEDULING_INTERVAL:15}
      url: ${xmplus.domain:https://dev.xmplus.cn}/cem/event/operate
  customer:
    scheduling:
      enabled: ${CTM_CUSTOMER_SCHEDULING_ENABLE:true}
      cron: ${CTM_CUSTOMER_SCHEDULING_CRON:0 0 1 * * *}
      batch-size: ${CTM_CUSTOMER_SCHEDULING_SIZE:100}
  journey:
    scheduling:
      enabled: ${CTM_JOURNEY_SCHEDULING_ENABLE:true}
      cron: ${CTM_JOURNEY_SCHEDULING_CRON:0 1 0 * * *}
      warnings: [ experience_indicator, event_stat ]
    journey-url: ${ctm.info.domain}cem/journeymap/
    email-template-add: journey-add-user
    email-template-remove: journey-remove-user
    email-template-indicator: journey-indicator-warning
    journey-size:
      empty: 0
      base: 1
      update: 5
      profession: 99999
  event:
    enable-notify: ${ENABLE_NOTIFY:true}
    default-group: ctm
    target-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/event/operateData/
    notify-topic: ctm_notify_event
    survey-response-topic: survey_response
    survey-change-topic: survey_change
    user-create-topic: queuing-user-create
  notify:
    app: cem
    warning: event-action-warning
    cooperation: event-action-cooperation
    close: event-action-close
    journey: journey-indicator-warning
    default-sms-template: default_sms_event
survey:
  client.enabled: false
  survey-url-prefix:
    root: ${LITE_INFO_DOMAIN:https://dev.xmplus.cn/lite/}
  download:
    attachment:
      saveDir: ${SURVEY_ATTACHMENT_SAVE_DIR:/tmp/attachment/}

